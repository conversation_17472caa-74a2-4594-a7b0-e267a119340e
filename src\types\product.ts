// Product interface based on shop_products table structure
export interface Product {
  id: string;
  name: string;
  description?: string;
  sku?: string;
  category_id?: string;
  subcategory_id?: string;
  brand?: string;
  unit_price: number;
  discount_price?: number;
  stock_quantity: number;
  moq?: number; // Minimum Order Quantity
  unit_of_measure?: string;
  hsn_code?: string;
  weight?: number;
  dimensions?: string;
  warranty_period?: string;
  image_url?: string;
  image_urls?: string[]; // Multiple images
  specifications?: string;
  low_stock_alert?: number;
  reorder_level?: number;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Cart item interface
export interface CartItem {
  product: Product;
  quantity: number;
}

// Category interface (already exists in page.tsx but creating a centralized version)
export interface Category {
  id: string;
  name: string;
  icon: string;
  description?: string;
  display_order?: number;
  is_active?: boolean;
}

// Cart context type
export interface CartContextType {
  items: CartItem[];
  addToCart: (product: Product, quantity?: number) => void;
  removeFromCart: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  getTotalItems: () => number;
  getTotalPrice: () => number;
}

// Product filter and sort options
export interface ProductFilters {
  priceRange?: {
    min: number;
    max: number;
  };
  brand?: string;
  inStock?: boolean;
  sortBy?: 'name' | 'price_low' | 'price_high' | 'newest';
}
