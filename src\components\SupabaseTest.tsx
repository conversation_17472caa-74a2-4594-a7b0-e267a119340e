'use client'

import { useState } from 'react'
import { database } from '@/lib/database'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { supabase } from '@/lib/supabase'

export default function SupabaseTest() {
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')

  const testConnection = async () => {
    setConnectionStatus('testing')
    try {
      const result = await database.testConnection()
      if (result.success) {
        setConnectionStatus('success')
        setMessage('✅ Supabase connection successful!')
      } else {
        setConnectionStatus('error')
        setMessage(`❌ Connection failed: ${result.error}`)
      }
    } catch (error) {
      setConnectionStatus('error')
      setMessage(`❌ Connection failed: ${error}`)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Supabase Connection Test</CardTitle>
        <CardDescription>
          Test your connection to the Supabase database
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={testConnection} 
          disabled={connectionStatus === 'testing'}
          className="w-full"
        >
          {connectionStatus === 'testing' ? 'Testing...' : 'Test Connection'}
        </Button>
        
        {message && (
          <div className={`p-3 rounded-md text-sm ${
            connectionStatus === 'success' 
              ? 'bg-green-50 text-green-700 border border-green-200' 
              : connectionStatus === 'error'
              ? 'bg-red-50 text-red-700 border border-red-200'
              : ''
          }`}>
            {message}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 