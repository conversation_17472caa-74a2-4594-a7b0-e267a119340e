'use client'

import { useState, useEffect } from "react";
import { supabase } from '@/lib/supabase';
import Image from "next/image";
import { useRouter } from "next/navigation";

import { useCart } from "@/contexts/CartContext";
import { 
  Search, 
  MapPin, 
  User, 
  ShoppingCart, 
  ChevronDown, 
  Crosshair, 
  Clock, 
  Truck, 
  Award, 
  Phone, 
  Mail,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Category {
  id: string;
  name: string;
  icon: string;
  description?: string;
  display_order?: number;
  is_active?: boolean;
}

export default function Home() {
  const router = useRouter();
  const { getTotalItems } = useCart();
  const [showCategoryNav, setShowCategoryNav] = useState(false);
  const [showMobileBanner, setShowMobileBanner] = useState(true);
  const [currentLocation, setCurrentLocation] = useState<string>("Select Location");
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string>("");
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);

  // Function to generate image path from category name
  const getImagePath = (categoryName: string) => {
    // Handle specific mappings for categories with special naming
    const imageMap: { [key: string]: string } = {
      'Hinges & Channels & handles': 'Hinges_and_Channels_and_handles.png',
      'Kitchen Systems & Accessories': 'Kitchen_Systems_and_Accessories.png',
      'Wardrobe & Bed Fittings': 'Wardrobe_and_Bed_Fittings.png',
      'Door Locks & Hardware': 'Door_Locks_and_Hardware.png',
      'CPVC Pipes & Fittings': 'CPVC_Pipes_and_Fittings.png',
      'Sanitary & Bath Fittings': 'Sanitary_and_Bath_Fittings.png',
      'Overhead Tanks': 'Overhead Tanks.png',
      'Conduits & GI Boxes': 'Conduits_and_GI_Boxes.png',
      'Switches & Sockets': 'Switches_and_Sockets.png',
      'Plywood MDF & HDHMR': 'Plywood_MDF_and_HDHMR.png',
      'Painting': 'Painting.png',
      'Cement': 'Cement.png',
      'Wires': 'Wires.png',
      'Waterproofing': 'Waterproofing.png',
      'Tiles': 'Tiles.png',
      'Fevicol': 'Fevicol.png',
      'Bricks': 'Bricks.png',
      'Sand': 'Sand.png',
      'Steel': 'Steel.png',
      'Tools': 'Tools.png'
    };

    // Check if there's a specific mapping
    if (imageMap[categoryName]) {
      return `/category/${imageMap[categoryName]}`;
    }

    // Default transformation: replace spaces with underscores
    const imageName = categoryName
      .replace(/\s+/g, '_')
      .replace(/&/g, 'and')
      .replace(/[^\w\s]/g, '');

    console.log(`Image path for ${categoryName}: /category/${imageName}.png`);
    return `/category/${imageName}.png`;
  };

  // Fetch categories from Supabase
  const fetchCategories = async () => {
    try {
      setIsLoadingCategories(true);
      const { data, error } = await supabase
        .from('shop_subcategories')
        .select('id, name, icon, description, display_order, is_active')
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }

      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setIsLoadingCategories(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // Function to detect Android device
  const isAndroid = () => {
    return /Android/i.test(navigator.userAgent);
  };

  // Function to handle "Use App" button click
  const handleUseAppClick = () => {
    if (isAndroid()) {
      // Redirect to Play Store for Android devices
      window.open('https://play.google.com/store/apps/details?id=com.infratasks.app', '_blank');
    } else {
      // For non-Android devices, you could redirect to App Store or show a message
      // For now, we'll just redirect to Play Store as well
      window.open('https://play.google.com/store/apps/details?id=com.infratasks.app', '_blank');
    }
  };

  // Function to reverse geocode coordinates to get address
  const reverseGeocode = async (lat: number, lng: number): Promise<string> => {
    try {
      const response = await fetch(
        `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${lng}&key=YOUR_API_KEY&limit=1`
      );
      
      if (!response.ok) {
        // Fallback to a free service if the API key is not available
        const fallbackResponse = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=10&addressdetails=1`
        );
        
        if (fallbackResponse.ok) {
          const data = await fallbackResponse.json();
          const city = data.address?.city || data.address?.town || data.address?.village || '';
          const state = data.address?.state || '';
          return city && state ? `${city}, ${state}` : data.display_name?.split(',').slice(0, 2).join(',') || 'Location detected';
        }
        throw new Error('Geocoding failed');
      }
      
      const data = await response.json();
      if (data.results && data.results.length > 0) {
        const result = data.results[0];
        const city = result.components.city || result.components.town || result.components.village;
        const state = result.components.state;
        return city && state ? `${city}, ${state}` : result.formatted.split(',').slice(0, 2).join(',');
      }
      throw new Error('No results found');
    } catch (error) {
      console.error('Geocoding error:', error);
      return 'Location detected';
    }
  };

  // Function to detect user's current location
  const detectLocation = () => {
    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by this browser');
      return;
    }

    setIsDetectingLocation(true);
    setLocationError('');

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        try {
          const address = await reverseGeocode(latitude, longitude);
          setCurrentLocation(address);
          setLocationError('');
        } catch (error) {
          console.error('Error getting address:', error);
          setCurrentLocation('Location detected');
        } finally {
          setIsDetectingLocation(false);
        }
      },
      (error) => {
        setIsDetectingLocation(false);
        switch (error.code) {
          case error.PERMISSION_DENIED:
            setLocationError('Location access denied by user');
            break;
          case error.POSITION_UNAVAILABLE:
            setLocationError('Location information is unavailable');
            break;
          case error.TIMEOUT:
            setLocationError('Location request timed out');
            break;
          default:
            setLocationError('An unknown error occurred');
            break;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      // Show category nav when scrolled past hero section (approximately 400px)
      setShowCategoryNav(scrollPosition > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile-Only App Download Banner */}
      {showMobileBanner && (
        <div className="md:hidden bg-green-600 text-white px-4 py-3 sticky top-0 z-50 shadow-sm">
          <div className="flex items-center justify-between">
            {/* Close button on the left */}
            <button
              onClick={() => setShowMobileBanner(false)}
              className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-green-700 transition-colors"
              aria-label="Close banner"
            >
              <X className="h-4 w-4" />
            </button>
            
            {/* Center content */}
            <div className="flex items-center space-x-2 flex-1 justify-center">
              <Image
                src="/favicon.png"
                alt="Infratask"
                width={20}
                height={20}
                className="rounded-sm"
              />
              <span className="text-sm font-medium">Get The App for Better Experience</span>
            </div>
            
            {/* Use App button on the right */}
            <Button 
              size="sm" 
              onClick={handleUseAppClick}
              className="bg-black hover:bg-gray-800 text-white rounded-lg px-4 py-1"
            >
              Use App
            </Button>
          </div>
        </div>
      )}

      {/* Enhanced Fixed Header */}
      <header className={`bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 fixed left-0 right-0 z-40 ${showMobileBanner ? 'md:top-0 top-12' : 'top-0'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo - Hidden on mobile */}
            <div className="hidden md:flex items-center">
              <Image
                src="/logo.png"
                alt="Infratask Logo"
                width={40}
                height={40}
                className="mr-3 rounded-lg"
              />
              <div className="text-2xl font-bold text-[#f97316]">
                Infratask
              </div>
            </div>

            {/* Location Dropdown - Visible on mobile */}
            <div className="block">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    className="flex items-center space-x-1 md:space-x-2 text-gray-700 hover:text-[#f97316] hover:bg-orange-50 rounded-xl px-2 md:px-3 py-2"
                  >
                    <MapPin className="h-4 w-4" />
                    <span className="text-xs md:text-sm font-medium hidden sm:inline max-w-32 truncate">
                      {isDetectingLocation ? "Detecting..." : currentLocation}
                    </span>
                    <span className="text-xs font-medium sm:hidden max-w-20 truncate">
                      {isDetectingLocation ? "..." : (currentLocation.length > 15 ? currentLocation.substring(0, 15) + "..." : currentLocation)}
                    </span>
                    <ChevronDown className="h-3 w-3 md:h-4 md:w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-64 rounded-xl shadow-lg">
                  <DropdownMenuItem 
                    className="rounded-lg cursor-pointer hover:bg-orange-50"
                    onClick={detectLocation}
                    disabled={isDetectingLocation}
                  >
                    <Crosshair className={`mr-2 h-4 w-4 ${isDetectingLocation ? 'animate-spin' : ''}`} />
                    <span>{isDetectingLocation ? 'Detecting location...' : 'Detect My Location'}</span>
                  </DropdownMenuItem>
                  
                  {locationError && (
                    <DropdownMenuItem className="rounded-lg text-red-600 text-xs">
                      <span>{locationError}</span>
                    </DropdownMenuItem>
                  )}
                  
                  <div className="h-px bg-gray-200 my-1"></div>
                  
                  <DropdownMenuItem 
                    className="rounded-lg cursor-pointer hover:bg-orange-50"
                    onClick={() => setCurrentLocation("Mumbai, Maharashtra")}
                  >
                    <MapPin className="mr-2 h-4 w-4" />
                    <span>Mumbai, Maharashtra</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    className="rounded-lg cursor-pointer hover:bg-orange-50"
                    onClick={() => setCurrentLocation("Delhi, NCR")}
                  >
                    <MapPin className="mr-2 h-4 w-4" />
                    <span>Delhi, NCR</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    className="rounded-lg cursor-pointer hover:bg-orange-50"
                    onClick={() => setCurrentLocation("Bangalore, Karnataka")}
                  >
                    <MapPin className="mr-2 h-4 w-4" />
                    <span>Bangalore, Karnataka</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    className="rounded-lg cursor-pointer hover:bg-orange-50"
                    onClick={() => setCurrentLocation("Chennai, Tamil Nadu")}
                  >
                    <MapPin className="mr-2 h-4 w-4" />
                    <span>Chennai, Tamil Nadu</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Search Bar - Hidden on mobile */}
            <div className="hidden md:flex flex-1 max-w-lg mx-4 sm:mx-8">
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input 
                  placeholder="Search construction materials..." 
                  className="pl-10 py-2 rounded-2xl border-gray-200 focus:border-[#f97316] focus:ring-[#f97316] bg-gray-50/50 backdrop-blur-sm w-full"
                />
              </div>
            </div>

            {/* Right Side - Full width on mobile */}
            <div className="flex items-center justify-end md:justify-start space-x-2 sm:space-x-4 flex-1 md:flex-none">
              {/* Project Management - Hidden on small screens */}
              <Button 
                variant="ghost" 
                className="hidden lg:inline-flex text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl"
              >
                Project Management
              </Button>
              
              {/* Login Button */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl hidden sm:inline-flex"
              >
                <User className="h-4 w-4 mr-2" />
                Login
              </Button>
              
              {/* Mobile Login Icon */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl sm:hidden p-2"
              >
                <User className="h-4 w-4" />
              </Button>
              
              {/* Shopping Cart */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl relative p-2"
              >
                <ShoppingCart className="h-4 w-4" />
                <Badge className="absolute -top-1 -right-1 bg-[#f97316] text-white rounded-full h-5 w-5 text-xs flex items-center justify-center">
                  3
                </Badge>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Category Navigation - Shows on Scroll */}
      <nav className={`bg-white/95 backdrop-blur-md border-b shadow-sm fixed left-0 right-0 z-30 transition-transform duration-300 ${
        showCategoryNav ? 'translate-y-0' : '-translate-y-full'
      } ${showMobileBanner ? 'md:top-16 top-28' : 'top-16'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-6 py-3 overflow-x-auto scrollbar-hide">
            {isLoadingCategories ? (
              // Loading skeleton
              Array.from({ length: 8 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-2 px-3 py-2 whitespace-nowrap">
                  <div className="w-8 h-8 rounded-full bg-gray-200 animate-pulse flex-shrink-0"></div>
                  <div className="w-16 h-4 bg-gray-200 animate-pulse rounded"></div>
                </div>
              ))
            ) : (
              categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => router.push(`/category/${category.id}`)}
                  className="flex items-center space-x-2 px-3 py-2 rounded-xl hover:bg-orange-50 transition-colors duration-200 whitespace-nowrap group"
                >
                  <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
                    <Image
                      src={getImagePath(category.name)}
                      alt={category.name}
                      width={32}
                      height={32}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/category/default.png';
                      }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-700 group-hover:text-[#f97316]">
                    {category.name}
                  </span>
                </button>
              ))
            )}
          </div>
        </div>
      </nav>

      {/* Asian Paints Sponsored Banner - No top spacing on mobile */}
      <section className="w-full px-5 md:px-[35px] pt-[45px]">
        <div className="h-[290px] w-full bg-gradient-to-r from-red-600 via-orange-500 to-yellow-500 relative overflow-hidden rounded-xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-8 left-8 w-24 h-24 border-2 border-white rounded-full"></div>
              <div className="absolute bottom-8 right-12 w-32 h-32 border-2 border-white rounded-full"></div>
              <div className="absolute top-16 right-1/3 w-16 h-16 border-2 border-white rounded-full"></div>
              <div className="absolute bottom-16 left-1/4 w-20 h-20 border-2 border-white rounded-full"></div>
            </div>
            
            {/* Content Container */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center justify-between relative z-10">
              {/* Left Content */}
              <div className="text-white max-w-md lg:max-w-lg">
                <div className="flex items-center mb-3">
                  <span className="bg-yellow-400 text-black text-xs px-4 py-2 rounded-full font-bold shadow-lg border border-white/30 animate-pulse">
                    ✨ SPONSORED
                  </span>
                </div>
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2 leading-tight">
                  Transform Your Space
                </h2>
                <h3 className="text-xl md:text-2xl font-semibold mb-3 text-yellow-100">
                  with Asian Paints
                </h3>
                <p className="text-sm md:text-base text-white/90 mb-4 leading-relaxed">
                  Premium quality paints for your construction projects. Get exclusive deals on bulk orders.
                </p>
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button className="bg-white text-red-600 hover:bg-gray-100 font-semibold px-6 py-2 rounded-xl transition-colors">
                    Shop Asian Paints
                  </Button>
                  <Button variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-red-600 font-semibold px-6 py-2 rounded-xl transition-colors">
                    Get Quote
                  </Button>
                </div>
              </div>
              
              {/* Right Content - Asian Paints Logo/Image Area */}
              <div className="hidden md:flex items-center justify-center">
                <div className="w-48 lg:w-64 h-32 lg:h-40 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 flex items-center justify-center relative overflow-hidden">
                  {/* Placeholder for Asian Paints Logo/Image */}
                  <div className="text-center text-white">
                    <div className="w-20 h-20 mx-auto mb-3 bg-white rounded-full flex items-center justify-center">
                      <span className="text-2xl font-bold text-red-600">AP</span>
                    </div>
                    <h4 className="text-lg font-bold">Asian Paints</h4>
                    <p className="text-sm text-white/80">Premium Quality</p>
                  </div>
                  
                  {/* Replace this section with actual Asian Paints image */}
                  {/* <Image
                    src="/asian-paints-logo.png"
                    alt="Asian Paints"
                    width={200}
                    height={120}
                    className="object-contain"
                  /> */}
                </div>
              </div>
            </div>
            
            {/* Paint Drop Effects */}
            <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
              <div className="absolute top-4 left-1/4 w-3 h-3 bg-white/30 rounded-full animate-bounce"></div>
              <div className="absolute top-8 right-1/3 w-2 h-2 bg-white/40 rounded-full animate-bounce" style={{animationDelay: '0.5s'}}></div>
              <div className="absolute bottom-12 left-1/3 w-4 h-4 bg-white/25 rounded-full animate-bounce" style={{animationDelay: '1s'}}></div>
            </div>
          </div>
                </section>

        {/* Category Section - Moved below sponsored banner */}
        <section className="py-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Shop by Category</h2>
            
            {isLoadingCategories ? (
              // Loading skeleton for category grid
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {Array.from({ length: 8 }).map((_, index) => (
                  <Card key={index} className="border border-gray-200">
                    <CardContent className="p-4 text-center">
                      <div className="w-12 h-12 mx-auto mb-2 rounded-lg bg-gray-200 animate-pulse"></div>
                      <div className="h-4 bg-gray-200 animate-pulse rounded mx-auto w-16"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : categories.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {categories.map((category) => (
                  <Card
                    key={category.id}
                    onClick={() => router.push(`/category/${category.id}`)}
                    className="cursor-pointer hover:shadow-lg transition-shadow duration-200 border border-gray-200 hover:border-[#f97316]"
                  >
                    <CardContent className="p-4 text-center">
                      <div className="w-12 h-12 mx-auto mb-2 rounded-lg overflow-hidden">
                        <Image
                          src={getImagePath(category.name)}
                          alt={category.name}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/category/default.png';
                          }}
                        />
                      </div>
                      <h3 className="text-sm font-medium text-gray-900">{category.name}</h3>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No categories available</p>
              </div>
            )}
          </div>
        </section>

      {/* Add padding to account for fixed header and mobile banner */}
        <div className={showMobileBanner ? "md:pt-16 pt-28" : "pt-16"}>
        {/* Hero Section - Scrollable Banners */}
        <section className="pb-6 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-hide">
              {/* Cement Deals Banner */}
              <Card className="min-w-[300px] md:min-w-[400px] bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold mb-2">Cement Deals</h3>
                      <p className="text-blue-100 mb-4">Premium cement at best prices</p>
                      <Button className="bg-[#f97316] hover:bg-[#ea580c] text-white">
                        Order Now
                      </Button>
                    </div>
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center overflow-hidden">
                      <Image
                        src="/category/Cement.png"
                        alt="Cement"
                        width={64}
                        height={64}
                        className="rounded-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/category/default.png';
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Plumbing Offers Banner */}
              <Card className="min-w-[300px] md:min-w-[400px] bg-gradient-to-r from-teal-500 to-teal-600 text-white border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold mb-2">Plumbing Offers</h3>
                      <p className="text-teal-100 mb-4">Complete plumbing solutions</p>
                      <Button className="bg-[#f97316] hover:bg-[#ea580c] text-white">
                        Order Now
                      </Button>
                    </div>
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center overflow-hidden">
                      <Image
                        src="/category/CPVC_Pipes_and_Fittings.png"
                        alt="Plumbing"
                        width={64}
                        height={64}
                        className="rounded-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/category/default.png';
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Paint & Finishes Banner */}
              <Card className="min-w-[300px] md:min-w-[400px] bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold mb-2">Paint & Finishes</h3>
                      <p className="text-purple-100 mb-4">Transform your spaces</p>
                      <Button className="bg-[#f97316] hover:bg-[#ea580c] text-white">
                        Order Now
                      </Button>
                    </div>
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center overflow-hidden">
                      <Image
                        src="/category/Painting.png"
                        alt="Paints"
                        width={64}
                        height={64}
                        className="rounded-full object-cover"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>





        {/* Featured Sections - 3 Column Layout */}
        <section className="py-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Vendor Highlight */}
              <Card className="shadow-lg border-0 bg-gradient-to-br from-orange-50 to-orange-100">
                <CardHeader className="text-center">
                  <Award className="h-12 w-12 text-[#f97316] mx-auto mb-2" />
                  <CardTitle className="text-gray-900">Top-Rated Vendor</CardTitle>
                  <CardDescription>Vendor of the week</CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <h4 className="font-semibold text-lg mb-2">BuildMart Pro</h4>
                  <div className="flex justify-center items-center mb-3">
                    <div className="flex text-yellow-400">
                      ⭐⭐⭐⭐⭐
                    </div>
                    <span className="ml-2 text-sm text-gray-600">4.9 (2.5k reviews)</span>
                  </div>
                  <Button className="bg-[#f97316] hover:bg-[#ea580c] text-white w-full">
                    View Store
                  </Button>
                </CardContent>
              </Card>

              {/* Material Delivery */}
              <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-green-100">
                <CardHeader className="text-center">
                  <Truck className="h-12 w-12 text-green-600 mx-auto mb-2" />
                  <CardTitle className="text-gray-900">Fast Delivery</CardTitle>
                  <CardDescription>Materials delivered quickly</CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="flex items-center justify-center mb-3">
                    <Clock className="h-5 w-5 text-green-600 mr-2" />
                    <span className="font-semibold text-lg">Delivered in 2 hours</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">Fast, reliable delivery to your construction site</p>
                  <Button className="bg-[#f97316] hover:bg-[#ea580c] text-white w-full">
                    Track Order
                  </Button>
                </CardContent>
              </Card>

              {/* Project Bundles */}
              <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-blue-100">
                <CardHeader className="text-center">
                  <MapPin className="h-12 w-12 text-blue-600 mx-auto mb-2" />
                  <CardTitle className="text-gray-900">Project Bundles</CardTitle>
                  <CardDescription>Complete site packages</CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <h4 className="font-semibold text-lg mb-2">Order by Site Type</h4>
                  <p className="text-sm text-gray-600 mb-4">Residential • Commercial • Industrial</p>
                  <Button className="bg-[#f97316] hover:bg-[#ea580c] text-white w-full">
                    Explore Bundles
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>



        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8 mt-16">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* Company Info */}
              <div>
                <h3 className="text-2xl font-bold text-[#f97316] mb-4">Infratask</h3>
                <p className="text-gray-300 mb-4">Your trusted partner for construction materials and project management solutions.</p>
                <div className="flex space-x-4">
                  <Phone className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-300">+****************</span>
                </div>
                <div className="flex space-x-4 mt-2">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-300"><EMAIL></span>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
                <ul className="space-y-2">
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Home</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">About</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Contact</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Help Center</a></li>
                </ul>
              </div>

              {/* Services */}
              <div>
                <h4 className="text-lg font-semibold mb-4">Services</h4>
                <ul className="space-y-2">
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors font-semibold">Vendor Registration</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors font-semibold">Project Management</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Material Sourcing</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Site Delivery</a></li>
                </ul>
              </div>

              {/* Categories */}
              <div>
                <h4 className="text-lg font-semibold mb-4">Categories</h4>
                <ul className="space-y-2">
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Cement & Concrete</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Steel & Metal</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Paints & Finishes</a></li>
                  <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Tools & Equipment</a></li>
                </ul>
              </div>
            </div>

            {/* Bottom Footer */}
            <div className="border-t border-gray-700 mt-8 pt-8 text-center">
              <p className="text-gray-400">&copy; 2024 Infratask. All rights reserved. Built for construction professionals.</p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}





