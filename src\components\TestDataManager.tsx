'use client'

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { insertSampleProducts, clearAllProducts } from '@/utils/testData';

export default function TestDataManager() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleInsertSampleData = async () => {
    setIsLoading(true);
    setMessage('');
    
    try {
      const result = await insertSampleProducts();
      if (result.success) {
        setMessage('✅ Sample products inserted successfully!');
      } else {
        setMessage(`❌ Error inserting products: ${result.error?.message || 'Unknown error'}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearData = async () => {
    setIsLoading(true);
    setMessage('');
    
    try {
      const result = await clearAllProducts();
      if (result.success) {
        setMessage('✅ All products cleared successfully!');
      } else {
        setMessage(`❌ Error clearing products: ${result.error?.message || 'Unknown error'}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Test Data Manager</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col gap-2">
          <Button 
            onClick={handleInsertSampleData}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Loading...' : 'Insert Sample Products'}
          </Button>
          
          <Button 
            onClick={handleClearData}
            disabled={isLoading}
            variant="destructive"
            className="w-full"
          >
            {isLoading ? 'Loading...' : 'Clear All Products'}
          </Button>
        </div>
        
        {message && (
          <div className="p-3 rounded-md bg-gray-50 text-sm">
            {message}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
