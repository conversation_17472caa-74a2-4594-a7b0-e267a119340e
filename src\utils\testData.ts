import { supabase } from '@/lib/supabase';
import { Product } from '@/types/product';

// Function to get available categories first
export async function getAvailableCategories() {
  try {
    const { data, error } = await supabase
      .from('shop_subcategories')
      .select('id, name')
      .eq('is_active', true)
      .order('display_order');

    if (error) {
      console.error('Error fetching categories:', error);
      return { success: false, error, data: [] };
    }

    console.log('Available categories:', data);
    return { success: true, data: data || [], error: null };
  } catch (error) {
    console.error('Error fetching categories:', error);
    return { success: false, error, data: [] };
  }
}

// Sample test products for different categories
export const sampleProducts: Omit<Product, 'id' | 'created_at' | 'updated_at'>[] = [
  {
    name: "Premium Portland Cement - 50kg",
    description: "High-quality Portland cement suitable for all construction needs. Meets IS 8112 standards.",
    sku: "CEM-PORT-50",
    subcategory_id: "25", // Cement category
    brand: "UltraTech",
    unit_price: 350,
    discount_price: 320,
    stock_quantity: 500,
    moq: 10,
    unit_of_measure: "bags",
    hsn_code: "25231000",
    weight: 50,
    specifications: "Grade: 43, Compressive Strength: 43 MPa, Setting Time: 30 min initial",
    low_stock_alert: 50,
    reorder_level: 100,
    is_active: true
  },
  {
    name: "OPC Cement - 50kg",
    description: "Ordinary Portland Cement for general construction work.",
    sku: "CEM-OPC-50",
    subcategory_id: "25",
    brand: "ACC",
    unit_price: 330,
    stock_quantity: 300,
    moq: 5,
    unit_of_measure: "bags",
    hsn_code: "25231000",
    weight: 50,
    specifications: "Grade: 53, High strength cement",
    low_stock_alert: 30,
    reorder_level: 80,
    is_active: true
  },
  {
    name: "TMT Steel Bars - 12mm",
    description: "High-strength TMT steel bars for reinforcement. Fe 500 grade.",
    sku: "STL-TMT-12",
    subcategory_id: "26", // Steel category (assuming)
    brand: "TATA Steel",
    unit_price: 65,
    discount_price: 62,
    stock_quantity: 1000,
    moq: 50,
    unit_of_measure: "pieces",
    hsn_code: "72142000",
    weight: 8.88,
    specifications: "Grade: Fe 500, Length: 12m, Diameter: 12mm",
    low_stock_alert: 100,
    reorder_level: 200,
    is_active: true
  },
  {
    name: "TMT Steel Bars - 16mm",
    description: "Heavy-duty TMT steel bars for structural work. Fe 500 grade.",
    sku: "STL-TMT-16",
    subcategory_id: "26",
    brand: "TATA Steel",
    unit_price: 85,
    stock_quantity: 800,
    moq: 25,
    unit_of_measure: "pieces",
    hsn_code: "72142000",
    weight: 15.81,
    specifications: "Grade: Fe 500, Length: 12m, Diameter: 16mm",
    low_stock_alert: 80,
    reorder_level: 150,
    is_active: true
  },
  {
    name: "Ceramic Wall Tiles - 300x600mm",
    description: "Premium ceramic wall tiles with glossy finish. Perfect for bathrooms and kitchens.",
    sku: "TIL-CER-30x60",
    subcategory_id: "27", // Tiles category (assuming)
    brand: "Kajaria",
    unit_price: 45,
    discount_price: 40,
    stock_quantity: 2000,
    moq: 100,
    unit_of_measure: "pieces",
    hsn_code: "69089000",
    specifications: "Size: 300x600mm, Thickness: 8mm, Finish: Glossy",
    low_stock_alert: 200,
    reorder_level: 500,
    is_active: true
  },
  {
    name: "Vitrified Floor Tiles - 600x600mm",
    description: "High-quality vitrified tiles for flooring. Slip-resistant surface.",
    sku: "TIL-VIT-60x60",
    subcategory_id: "27",
    brand: "Somany",
    unit_price: 85,
    stock_quantity: 1500,
    moq: 50,
    unit_of_measure: "pieces",
    hsn_code: "69089000",
    specifications: "Size: 600x600mm, Thickness: 10mm, Finish: Matt",
    low_stock_alert: 150,
    reorder_level: 300,
    is_active: true
  }
];

// Function to create sample products with actual category IDs
export async function createSampleProductsWithRealCategories() {
  try {
    // First get available categories
    const categoriesResult = await getAvailableCategories();
    if (!categoriesResult.success || categoriesResult.data.length === 0) {
      console.error('No categories available');
      return { success: false, error: 'No categories found' };
    }

    const categories = categoriesResult.data;
    console.log('Using categories:', categories);

    // Create sample products for the first few categories
    const sampleProductsWithRealIds = [];

    // For each category, create 2-3 sample products
    for (let i = 0; i < Math.min(categories.length, 5); i++) {
      const category = categories[i];

      // Create products based on category name
      if (category.name.toLowerCase().includes('cement')) {
        sampleProductsWithRealIds.push({
          name: "Premium Portland Cement - 50kg",
          description: "High-quality Portland cement suitable for all construction needs. Meets IS 8112 standards.",
          sku: `CEM-PORT-50-${category.id}`,
          subcategory_id: category.id,
          brand: "UltraTech",
          unit_price: 350,
          discount_price: 320,
          stock_quantity: 500,
          moq: 10,
          unit_of_measure: "bags",
          hsn_code: "25231000",
          weight: 50,
          specifications: "Grade: 43, Compressive Strength: 43 MPa, Setting Time: 30 min initial",
          low_stock_alert: 50,
          reorder_level: 100,
          is_active: true
        });

        sampleProductsWithRealIds.push({
          name: "OPC Cement - 50kg",
          description: "Ordinary Portland Cement for general construction work.",
          sku: `CEM-OPC-50-${category.id}`,
          subcategory_id: category.id,
          brand: "ACC",
          unit_price: 330,
          stock_quantity: 300,
          moq: 5,
          unit_of_measure: "bags",
          hsn_code: "25231000",
          weight: 50,
          specifications: "Grade: 53, High strength cement",
          low_stock_alert: 30,
          reorder_level: 80,
          is_active: true
        });
      } else if (category.name.toLowerCase().includes('steel')) {
        sampleProductsWithRealIds.push({
          name: "TMT Steel Bars - 12mm",
          description: "High-strength TMT steel bars for reinforcement. Fe 500 grade.",
          sku: `STL-TMT-12-${category.id}`,
          subcategory_id: category.id,
          brand: "TATA Steel",
          unit_price: 65,
          discount_price: 62,
          stock_quantity: 1000,
          moq: 50,
          unit_of_measure: "pieces",
          hsn_code: "72142000",
          weight: 8.88,
          specifications: "Grade: Fe 500, Length: 12m, Diameter: 12mm",
          low_stock_alert: 100,
          reorder_level: 200,
          is_active: true
        });
      } else if (category.name.toLowerCase().includes('tile')) {
        sampleProductsWithRealIds.push({
          name: "Ceramic Wall Tiles - 300x600mm",
          description: "Premium ceramic wall tiles with glossy finish. Perfect for bathrooms and kitchens.",
          sku: `TIL-CER-30x60-${category.id}`,
          subcategory_id: category.id,
          brand: "Kajaria",
          unit_price: 45,
          discount_price: 40,
          stock_quantity: 2000,
          moq: 100,
          unit_of_measure: "pieces",
          hsn_code: "69089000",
          specifications: "Size: 300x600mm, Thickness: 8mm, Finish: Glossy",
          low_stock_alert: 200,
          reorder_level: 500,
          is_active: true
        });
      } else {
        // Generic product for any category
        sampleProductsWithRealIds.push({
          name: `${category.name} - Premium Quality`,
          description: `High-quality ${category.name.toLowerCase()} for construction needs.`,
          sku: `GEN-${category.id}-001`,
          subcategory_id: category.id,
          brand: "Premium Brand",
          unit_price: 100,
          discount_price: 90,
          stock_quantity: 100,
          moq: 1,
          unit_of_measure: "pieces",
          hsn_code: "00000000",
          specifications: `Premium quality ${category.name.toLowerCase()}`,
          low_stock_alert: 10,
          reorder_level: 20,
          is_active: true
        });
      }
    }

    console.log('Sample products to insert:', sampleProductsWithRealIds);
    return { success: true, products: sampleProductsWithRealIds };
  } catch (error) {
    console.error('Error creating sample products:', error);
    return { success: false, error };
  }
}

// Function to insert sample products
export async function insertSampleProducts() {
  try {
    // First create products with real category IDs
    const createResult = await createSampleProductsWithRealCategories();
    if (!createResult.success) {
      return createResult;
    }

    const { data, error } = await supabase
      .from('shop_products')
      .insert(createResult.products)
      .select();

    if (error) {
      console.error('Error inserting sample products:', error);
      return { success: false, error };
    }

    console.log('Sample products inserted successfully:', data);
    return { success: true, data };
  } catch (error) {
    console.error('Error inserting sample products:', error);
    return { success: false, error };
  }
}

// Function to clear all products (for testing)
export async function clearAllProducts() {
  try {
    const { error } = await supabase
      .from('shop_products')
      .delete()
      .neq('id', ''); // Delete all records

    if (error) {
      console.error('Error clearing products:', error);
      return { success: false, error };
    }

    console.log('All products cleared successfully');
    return { success: true };
  } catch (error) {
    console.error('Error clearing products:', error);
    return { success: false, error };
  }
}
