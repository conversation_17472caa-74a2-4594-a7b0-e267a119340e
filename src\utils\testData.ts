import { supabase } from '@/lib/supabase';
import { Product } from '@/types/product';

// Sample test products for different categories
export const sampleProducts: Omit<Product, 'id' | 'created_at' | 'updated_at'>[] = [
  {
    name: "Premium Portland Cement - 50kg",
    description: "High-quality Portland cement suitable for all construction needs. Meets IS 8112 standards.",
    sku: "CEM-PORT-50",
    subcategory_id: "25", // Cement category
    brand: "UltraTech",
    unit_price: 350,
    discount_price: 320,
    stock_quantity: 500,
    moq: 10,
    unit_of_measure: "bags",
    hsn_code: "25231000",
    weight: 50,
    specifications: "Grade: 43, Compressive Strength: 43 MPa, Setting Time: 30 min initial",
    low_stock_alert: 50,
    reorder_level: 100,
    is_active: true
  },
  {
    name: "OPC Cement - 50kg",
    description: "Ordinary Portland Cement for general construction work.",
    sku: "CEM-OPC-50",
    subcategory_id: "25",
    brand: "ACC",
    unit_price: 330,
    stock_quantity: 300,
    moq: 5,
    unit_of_measure: "bags",
    hsn_code: "25231000",
    weight: 50,
    specifications: "Grade: 53, High strength cement",
    low_stock_alert: 30,
    reorder_level: 80,
    is_active: true
  },
  {
    name: "TMT Steel Bars - 12mm",
    description: "High-strength TMT steel bars for reinforcement. Fe 500 grade.",
    sku: "STL-TMT-12",
    subcategory_id: "26", // Steel category (assuming)
    brand: "TATA Steel",
    unit_price: 65,
    discount_price: 62,
    stock_quantity: 1000,
    moq: 50,
    unit_of_measure: "pieces",
    hsn_code: "72142000",
    weight: 8.88,
    specifications: "Grade: Fe 500, Length: 12m, Diameter: 12mm",
    low_stock_alert: 100,
    reorder_level: 200,
    is_active: true
  },
  {
    name: "TMT Steel Bars - 16mm",
    description: "Heavy-duty TMT steel bars for structural work. Fe 500 grade.",
    sku: "STL-TMT-16",
    subcategory_id: "26",
    brand: "TATA Steel",
    unit_price: 85,
    stock_quantity: 800,
    moq: 25,
    unit_of_measure: "pieces",
    hsn_code: "72142000",
    weight: 15.81,
    specifications: "Grade: Fe 500, Length: 12m, Diameter: 16mm",
    low_stock_alert: 80,
    reorder_level: 150,
    is_active: true
  },
  {
    name: "Ceramic Wall Tiles - 300x600mm",
    description: "Premium ceramic wall tiles with glossy finish. Perfect for bathrooms and kitchens.",
    sku: "TIL-CER-30x60",
    subcategory_id: "27", // Tiles category (assuming)
    brand: "Kajaria",
    unit_price: 45,
    discount_price: 40,
    stock_quantity: 2000,
    moq: 100,
    unit_of_measure: "pieces",
    hsn_code: "69089000",
    specifications: "Size: 300x600mm, Thickness: 8mm, Finish: Glossy",
    low_stock_alert: 200,
    reorder_level: 500,
    is_active: true
  },
  {
    name: "Vitrified Floor Tiles - 600x600mm",
    description: "High-quality vitrified tiles for flooring. Slip-resistant surface.",
    sku: "TIL-VIT-60x60",
    subcategory_id: "27",
    brand: "Somany",
    unit_price: 85,
    stock_quantity: 1500,
    moq: 50,
    unit_of_measure: "pieces",
    hsn_code: "69089000",
    specifications: "Size: 600x600mm, Thickness: 10mm, Finish: Matt",
    low_stock_alert: 150,
    reorder_level: 300,
    is_active: true
  }
];

// Function to insert sample products
export async function insertSampleProducts() {
  try {
    const { data, error } = await supabase
      .from('shop_products')
      .insert(sampleProducts)
      .select();

    if (error) {
      console.error('Error inserting sample products:', error);
      return { success: false, error };
    }

    console.log('Sample products inserted successfully:', data);
    return { success: true, data };
  } catch (error) {
    console.error('Error inserting sample products:', error);
    return { success: false, error };
  }
}

// Function to clear all products (for testing)
export async function clearAllProducts() {
  try {
    const { error } = await supabase
      .from('shop_products')
      .delete()
      .neq('id', ''); // Delete all records

    if (error) {
      console.error('Error clearing products:', error);
      return { success: false, error };
    }

    console.log('All products cleared successfully');
    return { success: true };
  } catch (error) {
    console.error('Error clearing products:', error);
    return { success: false, error };
  }
}
