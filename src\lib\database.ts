import { supabase } from './supabase'

// Example functions for common database operations
export const database = {
  // Test connection
  async testConnection() {
    try {
      const { data, error } = await supabase.from('').select('').limit(1)
      if (error && error.code !== 'PGRST116') { // PGRST116 is expected when no table exists
        throw error
      }
      return { success: true, message: 'Connection successful' }
    } catch (error) {
      return { success: false, error: error }
    }
  },

  // Get all records from a table
  async getAll(tableName: string) {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
    
    if (error) {
      console.error('Error fetching data:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  },

  // Get a single record by ID
  async getById(tableName: string, id: string | number) {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      console.error('Error fetching record:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  },

  // Insert a new record
  async insert(tableName: string, record: Record<string, any>) {
    const { data, error } = await supabase
      .from(tableName)
      .insert(record)
      .select()
    
    if (error) {
      console.error('Error inserting record:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  },

  // Update a record
  async update(tableName: string, id: string | number, updates: Record<string, any>) {
    const { data, error } = await supabase
      .from(tableName)
      .update(updates)
      .eq('id', id)
      .select()
    
    if (error) {
      console.error('Error updating record:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  },

  // Delete a record
  async delete(tableName: string, id: string | number) {
    const { data, error } = await supabase
      .from(tableName)
      .delete()
      .eq('id', id)
    
    if (error) {
      console.error('Error deleting record:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  }
}

export default database 