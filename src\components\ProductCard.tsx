'use client'

import React, { useState } from 'react';
import Image from 'next/image';
import { Product } from '@/types/product';
import { useCart } from '@/contexts/CartContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, Plus, Minus } from 'lucide-react';

interface ProductCardProps {
  product: Product;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const { items, addToCart, updateQuantity } = useCart();
  const [isLoading, setIsLoading] = useState(false);

  // Find if product is already in cart
  const cartItem = items.find(item => item.product.id === product.id);
  const quantityInCart = cartItem?.quantity || 0;

  const handleAddToCart = async () => {
    setIsLoading(true);
    try {
      addToCart(product, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleIncrement = () => {
    if (cartItem) {
      updateQuantity(product.id, cartItem.quantity + 1);
    } else {
      addToCart(product, 1);
    }
  };

  const handleDecrement = () => {
    if (cartItem && cartItem.quantity > 1) {
      updateQuantity(product.id, cartItem.quantity - 1);
    } else if (cartItem && cartItem.quantity === 1) {
      updateQuantity(product.id, 0); // This will remove the item
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const discountPercentage = product.discount_price && product.unit_price > product.discount_price
    ? Math.round(((product.unit_price - product.discount_price) / product.unit_price) * 100)
    : 0;

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 border border-gray-200 hover:border-[#f97316] overflow-hidden">
      <CardContent className="p-0">
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden bg-gray-50">
          <Image
            src={product.image_url || '/placeholder-product.svg'}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/placeholder-product.svg';
            }}
          />
          
          {/* Discount Badge */}
          {discountPercentage > 0 && (
            <Badge className="absolute top-2 left-2 bg-red-500 text-white">
              {discountPercentage}% OFF
            </Badge>
          )}

          {/* Stock Status */}
          {product.stock_quantity <= 0 && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <Badge variant="destructive" className="text-sm">
                Out of Stock
              </Badge>
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="p-4">
          {/* Brand */}
          {product.brand && (
            <p className="text-xs text-gray-500 mb-1">{product.brand}</p>
          )}

          {/* Product Name */}
          <h3 className="font-medium text-gray-900 mb-2 line-clamp-2 text-sm leading-tight">
            {product.name}
          </h3>

          {/* Price */}
          <div className="mb-3">
            {product.discount_price ? (
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold text-[#f97316]">
                  {formatPrice(product.discount_price)}
                </span>
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.unit_price)}
                </span>
              </div>
            ) : (
              <span className="text-lg font-bold text-gray-900">
                {formatPrice(product.unit_price)}
              </span>
            )}
            
            {/* Unit of Measure */}
            {product.unit_of_measure && (
              <p className="text-xs text-gray-500 mt-1">
                per {product.unit_of_measure}
              </p>
            )}
          </div>

          {/* MOQ Info */}
          {product.moq && product.moq > 1 && (
            <p className="text-xs text-blue-600 mb-2">
              Min. Order: {product.moq} {product.unit_of_measure || 'units'}
            </p>
          )}

          {/* Add to Cart / Counter */}
          {product.stock_quantity > 0 ? (
            quantityInCart > 0 ? (
              <div className="flex items-center justify-between bg-[#f97316] rounded-lg p-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDecrement}
                  className="h-8 w-8 p-0 text-white hover:bg-white/20"
                >
                  <Minus className="h-4 w-4" />
                </Button>
                
                <span className="text-white font-medium px-2">
                  {quantityInCart}
                </span>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleIncrement}
                  className="h-8 w-8 p-0 text-white hover:bg-white/20"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <Button
                onClick={handleAddToCart}
                disabled={isLoading}
                className="w-full bg-[#f97316] hover:bg-[#ea580c] text-white"
                size="sm"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Adding...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <ShoppingCart className="h-4 w-4" />
                    Add to Cart
                  </div>
                )}
              </Button>
            )
          ) : (
            <Button disabled className="w-full" size="sm">
              Out of Stock
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
