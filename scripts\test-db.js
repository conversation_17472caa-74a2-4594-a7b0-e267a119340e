const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('Testing Supabase connection...');
  
  try {
    const { data, error } = await supabase.from('shop_subcategories').select('count').limit(1);
    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    } else {
      console.log('✅ Database connection successful');
      return true;
    }
  } catch (error) {
    console.error('❌ Connection error:', error.message);
    return false;
  }
}

async function getCategories() {
  console.log('\nFetching categories...');
  
  try {
    const { data, error } = await supabase
      .from('shop_subcategories')
      .select('id, name, is_active')
      .order('display_order');
    
    if (error) {
      console.error('❌ Failed to fetch categories:', error.message);
      return [];
    }
    
    console.log(`✅ Found ${data.length} categories:`);
    data.forEach(cat => {
      console.log(`  - ${cat.name} (ID: ${cat.id}, Active: ${cat.is_active})`);
    });
    
    return data;
  } catch (error) {
    console.error('❌ Error fetching categories:', error.message);
    return [];
  }
}

async function inspectProductsTable() {
  console.log('\nInspecting products table structure...');

  try {
    // Try to get any record to see the actual column names
    const { data, error } = await supabase
      .from('shop_products')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Failed to inspect products table:', error.message);

      // Try with minimal select to see if table exists
      const { data: minData, error: minError } = await supabase
        .from('shop_products')
        .select('id')
        .limit(1);

      if (minError) {
        console.error('❌ Products table might not exist:', minError.message);
      } else {
        console.log('✅ Products table exists but has different column structure');
      }
      return [];
    }

    if (data && data.length > 0) {
      console.log('✅ Products table structure (sample record):');
      console.log(JSON.stringify(data[0], null, 2));
    } else {
      console.log('✅ Products table exists but is empty');

      // Try to get table info by attempting different common column names
      const commonColumns = ['id', 'name', 'category_id', 'subcategory_id', 'sub_category_id', 'price', 'unit_price', 'is_active', 'active'];

      for (const col of commonColumns) {
        try {
          const { error: colError } = await supabase
            .from('shop_products')
            .select(col)
            .limit(1);

          if (!colError) {
            console.log(`  ✅ Column exists: ${col}`);
          }
        } catch (e) {
          // Column doesn't exist, continue
        }
      }
    }

    return data || [];
  } catch (error) {
    console.error('❌ Error inspecting products table:', error.message);
    return [];
  }
}

async function getProducts() {
  console.log('\nFetching products...');

  try {
    const { data, error } = await supabase
      .from('shop_products')
      .select('*')
      .limit(20);

    if (error) {
      console.error('❌ Failed to fetch products:', error.message);
      return [];
    }

    console.log(`✅ Found ${data.length} products:`);
    data.forEach(product => {
      console.log(`  - ${product.name || product.title || 'Unknown'} (ID: ${product.id})`);
    });

    return data;
  } catch (error) {
    console.error('❌ Error fetching products:', error.message);
    return [];
  }
}

async function insertSampleProducts(categories) {
  if (categories.length === 0) {
    console.log('❌ No categories available to insert products');
    return;
  }
  
  console.log('\nInserting sample products...');
  
  const sampleProducts = [];
  
  // Create 2-3 products for each of the first 3 categories
  for (let i = 0; i < Math.min(categories.length, 3); i++) {
    const category = categories[i];
    
    sampleProducts.push({
      name: `${category.name} - Premium Quality`,
      description: `High-quality ${category.name.toLowerCase()} for construction needs.`,
      sku: `PROD-${category.id}-001`,
      subcategory_id: category.id,
      brand: "Premium Brand",
      unit_price: 100,
      discount_price: 90,
      stock_quantity: 100,
      moq: 1,
      unit_of_measure: "pieces",
      hsn_code: "00000000",
      specifications: `Premium quality ${category.name.toLowerCase()}`,
      low_stock_alert: 10,
      reorder_level: 20,
      is_active: true
    });
    
    sampleProducts.push({
      name: `${category.name} - Standard Quality`,
      description: `Standard quality ${category.name.toLowerCase()} for everyday use.`,
      sku: `PROD-${category.id}-002`,
      subcategory_id: category.id,
      brand: "Standard Brand",
      unit_price: 75,
      stock_quantity: 200,
      moq: 1,
      unit_of_measure: "pieces",
      hsn_code: "00000000",
      specifications: `Standard quality ${category.name.toLowerCase()}`,
      low_stock_alert: 20,
      reorder_level: 40,
      is_active: true
    });
  }
  
  try {
    const { data, error } = await supabase
      .from('shop_products')
      .insert(sampleProducts)
      .select();
    
    if (error) {
      console.error('❌ Failed to insert products:', error.message);
      console.error('Error details:', error);
    } else {
      console.log(`✅ Inserted ${data.length} sample products`);
    }
  } catch (error) {
    console.error('❌ Error inserting products:', error.message);
  }
}

async function clearProducts() {
  console.log('\nClearing all products...');
  
  try {
    const { error } = await supabase
      .from('shop_products')
      .delete()
      .neq('id', ''); // Delete all records
    
    if (error) {
      console.error('❌ Failed to clear products:', error.message);
    } else {
      console.log('✅ All products cleared');
    }
  } catch (error) {
    console.error('❌ Error clearing products:', error.message);
  }
}

async function main() {
  console.log('=== Database Test Script ===\n');
  
  // Test connection
  const connected = await testConnection();
  if (!connected) {
    process.exit(1);
  }
  
  // Get categories
  const categories = await getCategories();

  // Inspect products table structure
  await inspectProductsTable();

  // Get existing products
  const products = await getProducts();
  
  // If no products exist, insert sample data
  if (products.length === 0) {
    console.log('\nNo products found. Inserting sample data...');
    await insertSampleProducts(categories);
    
    // Check products again
    await getProducts();
  } else {
    console.log('\nProducts already exist. Use --clear flag to clear them first.');
  }
  
  console.log('\n=== Test Complete ===');
}

// Check for command line arguments
const args = process.argv.slice(2);
if (args.includes('--clear')) {
  clearProducts().then(() => {
    console.log('Products cleared. Run script again to insert sample data.');
  });
} else {
  main();
}
